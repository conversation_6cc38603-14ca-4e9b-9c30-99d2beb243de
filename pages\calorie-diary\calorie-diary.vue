<template>
	<view class="container">
		<!-- 自定义头部 -->
		<view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="header-content">
				<view class="header-left">
					<image class="avatar" src="/static/images/calorie_diary/icon-customer-service.svg" mode="aspectFit">
					</image>
					<text class="greeting">联系客服</text>
				</view>
				<view class="header-center">
					<text class="title">热量日记</text>
				</view>
			</view>

			<!-- 记录提示 -->
			<view class="record-tip">
				<text class="tip-text">3秒记录+定制方案+精准控卡</text>
				<view class="start-btn">
					<image class="reminder-icon" src="/static/images/calorie_diary/icon-reminder.svg" mode="aspectFit">
					</image>
					<text class="btn-text">开启提醒</text>
				</view>
			</view>
		</view>

		<!-- 页面内容 -->
		<view class="page-content">

			<!-- 今日热量剩余 -->
			<view class="calorie-section">
				<view class="section-header">
					<text class="section-title">今日热量剩余</text>
					<view class="tip-btn">
						<image class="tip-btn-icon" src="/static/images/calorie_diary/icon-edit.svg" mode="aspectFit">
						</image>
						<text class="tip-btn-text">定制热量方案</text>
					</view>
				</view>

				<view class="calorie-display">
					<view class="calorie-content">
						<view class="calorie-info-row">
							<text class="calorie-label">今日还可吃</text>
							<text class="number">640</text>
							<image class="fruit-icon" src="/static/images/calorie_diary/<EMAIL>" mode="aspectFit">
							</image>
						</view>
						<view class="progress-bar">
							<view class="progress-fill"></view>
						</view>
					</view>
				</view>

				<!-- 热量统计 -->
				<view class="calorie-stats">
					<view class="stat-item">
						<text class="stat-label">每日热量计划</text>
						<view class="stat-value-row">
							<text class="stat-value">1200</text>
							<image class="stat-edit-icon" src="/static/images/calorie_diary/icon-edit.svg"
								mode="aspectFit"></image>
						</view>
					</view>
					<view class="stat-divider-item">
						<view class="stat-value-row">
							<text class="stat-divider">-</text>
						</view>
					</view>
					<view class="stat-item">
						<text class="stat-label">已经吃了</text>
						<view class="stat-value-row">
							<text class="stat-value">560</text>
						</view>
					</view>
					<view class="stat-divider-item">
						<view class="stat-value-row">
							<text class="stat-divider">+</text>
						</view>
					</view>
					<view class="stat-item">
						<text class="stat-label">运动消耗</text>
						<view class="stat-value-row">
							<text class="stat-value">0</text>
						</view>
					</view>
				</view>

				<!-- 灰色条 -->
				<view class="gray-bar">
					<image class="launch-icon" src="/static/images/calorie_diary/icon-launch.svg" mode="aspectFit">
					</image>
				</view>
			</view>

			<!-- 今日热量明细 -->
			<view class="detail-section">
				<text class="section-title">今日热量明细</text>
				<view class="input-container">
					<view class="detail-content">
						<textarea class="detail-input"
							placeholder="像聊天一样记录热量和体重。比如：午餐吃了半碗糙米饭，下午吃了一小袋混合坚果，晚上骑共享单车回家大概15分钟。今天体重99斤。也可以对食物进行拍照自动识别分析热量。"></textarea>
					</view>

					<view class="action-buttons">
						<view class="action-btn photo-btn">
							<image class="btn-icon" src="/static/images/calorie_diary/icon-camera.svg" mode="aspectFit">
							</image>
							<text class="btn-text">拍照识别</text>
						</view>
						<view class="action-btn voice-btn">
							<image class="btn-icon" src="/static/images/calorie_diary/icon-voice.svg" mode="aspectFit">
							</image>
							<text class="btn-text">语音输入</text>
						</view>
					</view>
				</view>

				<view class="record-btn">
					<text class="record-btn-text">热量分析&记录</text>
				</view>

				<!-- 体重和步数 -->
				<view class="stats-section">
					<view class="stat-card weight-card">
						<view class="card-header">
							<text class="stat-title">今日体重</text>
							<image class="more-dots-icon" src="/static/images/calorie_diary/icon-more.svg" mode="aspectFit">
							</image>
						</view>
						<view class="card-content">
							<view class="value-display">
								<text class="stat-number">62.4</text>
								<text class="stat-unit">kg</text>
							</view>
							<image class="feature-icon" src="/static/images/calorie_diary/icon-weight.svg" mode="aspectFit">
							</image>
						</view>
					</view>

					<view class="stat-card steps-card">
						<view class="card-header">
							<text class="stat-title">微信步数</text>
							<image class="more-dots-icon" src="/static/images/calorie_diary/icon-more.svg" mode="aspectFit">
							</image>
						</view>
						<view class="card-content">
							<view class="value-display">
								<text class="stat-number">--</text>
								<text class="stat-unit">步数</text>
							</view>
							<image class="feature-icon" src="/static/images/calorie_diary/icon-step.svg" mode="aspectFit">
							</image>
						</view>
					</view>
				<!-- 食物记录列表 -->
				<view class="food-list">
					<view class="food-item">
						<view class="food-info">
							<view class="food-icon orange"></view>
							<view class="food-details">
								<text class="food-name">牛奶</text>
								<text class="food-amount">300毫升</text>
								<view class="food-nutrition">
									<text class="nutrition-item">碳水 0.6g</text>
									<text class="nutrition-item">蛋白质 7.8g</text>
									<text class="nutrition-item">脂肪 6.2g</text>
								</view>
							</view>
						</view>
						<view class="food-calorie">
							<text class="calorie-text">186千卡</text>
							<image class="arrow-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
						</view>
					</view>

					<view class="food-item">
						<view class="food-info">
							<view class="food-icon green"></view>
							<view class="food-details">
								<text class="food-name">骑行</text>
								<text class="food-amount">15分钟</text>
							</view>
						</view>
						<view class="food-calorie">
							<text class="calorie-text">30千卡</text>
							<image class="arrow-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
						</view>
					</view>

					<view class="food-item">
						<view class="food-info">
							<view class="food-icon orange"></view>
							<view class="food-details">
								<text class="food-name">坚果</text>
								<text class="food-amount">60克</text>
								<view class="food-nutrition">
									<text class="nutrition-item">碳水 0.8g</text>
									<text class="nutrition-item">蛋白质 9.6g</text>
									<text class="nutrition-item">脂肪 1.2g</text>
								</view>
							</view>
						</view>
						<view class="food-calorie">
							<text class="calorie-text">696千卡</text>
							<image class="arrow-icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0
			}
		},
		onLoad() {
			this.getSystemInfo()
		},
		methods: {
			getSystemInfo() {
				uni.getSystemInfo({
					success: (res) => {
						this.statusBarHeight = res.statusBarHeight || 20
					}
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	/* 自定义头部 */
	.custom-header {
		background: linear-gradient(to bottom, #E3FFFA, #F2F2F2);
		position: static;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		padding-bottom: 30rpx;
	}

	.header-content {
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 30rpx;
		position: relative;
	}

	.header-left {
		display: flex;
		align-items: center;
		min-width: 140rpx;
		position: absolute;
		left: 30rpx;
		top: 104rpx;
		transform: translateY(-50%);
	}

	.avatar {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		margin-right: 12rpx;
	}

	.greeting {
		font-size: 28rpx;
		color: #666666;
		font-weight: bold;
	}

	.header-center {
		position: absolute;
		left: 50%;
		top: 104rpx;
		transform: translate(-50%, -50%);
	}

	.title {
		font-size: 40rpx;
		font-weight: 600;
		color: #333333;
		letter-spacing: 1rpx;
		font-family: "PingFang SC-Bold", "Microsoft YaHei", sans-serif;
	}


	
	/* 页面内容 */
	.page-content {
		padding-top: 20rpx;
	}

	/* 记录提示 */
	.record-tip {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx 0 30rpx;
		margin-top: 42rpx;
	}
	
	.tip-text {
		font-size: 34rpx;
		color: #333333;
		font-weight: bold;
	}

	.start-btn {
		width: 164rpx;
		height: 52rpx;
		background: #FFFFFF;
		border-radius: 36rpx;
		border: 0rpx solid #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.reminder-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 8rpx;
	}

	.btn-text {
		font-size: 24rpx;
		color: #666666;
		font-weight: 500;
		text-align: center;
		line-height: 1;
	}
	
	/* 热量部分 */
	.calorie-section {
		background-color: #ffffff;
		margin: 0 20rpx 20rpx 20rpx;
		padding: 40rpx 30rpx;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.section-title {
		font-size: 34rpx;
		font-weight: 600;
		color: #333333;
	}
	
	.tip-btn {
		width: 208rpx;
		height: 53rpx;
		background: #FFFFFF;
		border: 1rpx solid #13CEAD;
		border-radius: 18rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
	}

	.tip-btn-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
		flex-shrink: 0;
	}

	.tip-btn-text {
		font-weight: 500;
		font-size: 24rpx;
		color: #13CEAD;
		line-height: 36rpx;
		text-align: center;
		flex-shrink: 0;
	}
	
	.calorie-display {
		margin-bottom: 40rpx;
		padding-top: 2rpx;
	}

	.calorie-content {
		display: flex;
		flex-direction: column;
	}

	.calorie-info-row {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		margin-bottom: 24rpx;
		width: 100%;
	}

	.calorie-label {
		width: 120rpx;
		height: 36rpx;
		font-weight: 500;
		font-size: 24rpx;
		color: #666666;
		line-height: 36rpx;
		text-align: left;
	}

	.number {
		width: 130rpx;
		height: 72rpx;
		font-weight: bold;
		font-size: 72rpx;
		color: #13CEAD;
		line-height: 72rpx;
		text-align: left;
		margin-left: 14rpx;
	}

	.unit {
		width: 48rpx;
		height: 36rpx;
		font-weight: 500;
		font-size: 24rpx;
		color: #13CEAD;
		line-height: 36rpx;
		text-align: left;
		margin-left: 16rpx;
	}
	
	.progress-bar {
		width: 100%;
		height: 8rpx;
		background-color: #E5E5E5;
		border-radius: 4rpx;
		overflow: hidden;
	}
	
	.progress-fill {
		width: 53%;
		height: 100%;
		background-color: #00D4AA;
	}
	
	.fruit-icon {
		width: 140rpx;
		height: 140rpx;
		border-radius: 50%;
		background-color: #f8f8f8;
		margin-left: auto;
		flex-shrink: 0;
	}
	
	.calorie-stats {
		display: flex;
		justify-content: center;
		align-items: flex-end;
		gap: 20rpx;
	}
	
	.stat-item {
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		min-width: 144rpx;
		flex-shrink: 0;
	}

	.stat-divider-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-end;
		min-width: 60rpx;
		flex-shrink: 0;
	}
	
	.stat-label {
		width: 144rpx;
		height: 36rpx;
		font-weight: 500;
		font-size: 24rpx;
		color: #666666;
		line-height: 36rpx;
		text-align: center;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.stat-value-row {
		display: flex;
		align-items: baseline;
		justify-content: center;
		height: 64rpx;
		min-width: 100%;
		flex-shrink: 0;
	}

	.stat-value {
		font-weight: 500;
		font-size: 48rpx;
		color: #343738;
		line-height: 64rpx;
		text-align: center;
	}

	.stat-edit-icon {
		width: 24rpx;
		height: 24rpx;
		margin-left: 8rpx;
		flex-shrink: 0;
	}
	
	.stat-divider {
		width: 30rpx;
		height: 48rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 48rpx;
		color: #343738;
		line-height: 48rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}

	.gray-bar {
		width: 626rpx;
		height: 48rpx;
		background: #F9F9F9;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 20rpx auto 6rpx auto;
	}

	.launch-icon {
		width: 28rpx;
		height: 28rpx;
	}

	/* 明细部分 */
	.detail-section {
		background-color: #F9F9F9;
		margin: 0 20rpx 20rpx 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}
	
	.input-container {
		width: 626rpx;
		height: 324rpx;
		background: #F9F9F9;
		border-radius: 16rpx;
		margin: 24rpx auto 32rpx auto;
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.detail-content {
		flex: 1;
		margin-bottom: 16rpx;
	}
	
	.detail-input {
		width: 578rpx;
		height: 200rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 28rpx;
		color: #666666;
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
		background: transparent;
		border: none;
		outline: none;
		resize: none;
		padding: 0;
		margin: 0;
	}
	
	.action-buttons {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 48rpx;
		flex-shrink: 0;
	}
	
	.action-btn {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		width: 180rpx;
		height: 52rpx;
		background: #FFFFFF;
		border-radius: 40rpx;
		padding: 16rpx 24rpx;
	}
	
	.btn-icon {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
		flex-shrink: 0;
	}
	
	.record-btn {
		width: 480rpx;
		height: 80rpx;
		background: #13CEAD;
		border-radius: 48rpx;
		text-align: center;
		box-shadow: 0 4rpx 16rpx rgba(19, 206, 173, 0.3);
		margin: 0 auto;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.record-btn-text {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #FFFFFF;
		text-align: center;
		font-style: normal;
		text-transform: none;
		line-height: 1;
		white-space: nowrap;
		flex-shrink: 0;
	}
	
	/* 统计部分 */
	.stats-section {
		display: flex;
		margin: 32rpx 0 20rpx 0;
		gap: 20rpx;
		flex-direction: row;
	}
	
	.stat-card {
		background-color: #ffffff;
		padding: 24rpx;
		border-radius: 16rpx;
		flex: 1;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 160rpx;
	}
	
	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.stat-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
	}

	.more-dots-icon {
		width: 24rpx;
		height: 24rpx;
	}
	
	.card-content {
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
	}

	.value-display {
		display: flex;
		align-items: baseline;
		flex: 1;
	}

	.stat-number {
		font-size: 56rpx;
		font-weight: 600;
		color: #333333;
		line-height: 1;
	}

	.stat-unit {
		font-size: 28rpx;
		color: #999999;
		margin-left: 8rpx;
		font-weight: 400;
	}

	.feature-icon {
		width: 48rpx;
		height: 48rpx;
		flex-shrink: 0;
	}
	

	
	/* 食物列表 */
	.food-list {
		margin: 20rpx 0 0 0;
	}

	.food-item {
		background-color: #ffffff;
		padding: 30rpx;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}
	
	.food-info {
		display: flex;
		align-items: center;
		flex: 1;
	}
	
	.food-icon {
		width: 20rpx;
		height: 20rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}
	
	.food-icon.orange {
		background-color: #FF9500;
	}
	
	.food-icon.green {
		background-color: #00D4AA;
	}
	
	.food-details {
		flex: 1;
	}
	
	.food-name {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.food-amount {
		font-size: 24rpx;
		color: #666666;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.food-nutrition {
		display: flex;
		gap: 20rpx;
	}
	
	.nutrition-item {
		font-size: 22rpx;
		color: #999999;
	}
	
	.food-calorie {
		display: flex;
		align-items: center;
	}
	
	.calorie-text {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
		margin-right: 10rpx;
	}
	
	.arrow-icon {
		width: 20rpx;
		height: 20rpx;
	}
</style>
